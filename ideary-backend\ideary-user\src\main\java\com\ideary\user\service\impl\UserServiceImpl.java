package com.ideary.user.service.impl;

import com.ideary.user.dto.*;
import com.ideary.user.entity.User;
import com.ideary.user.entity.UserProfile;
import com.ideary.user.entity.UserFollow;
import com.ideary.user.mapper.UserMapper;
import com.ideary.user.mapper.UserProfileMapper;
import com.ideary.user.mapper.UserFollowMapper;
import com.ideary.user.service.UserService;
import com.ideary.user.util.JwtUtil;
import com.ideary.user.util.PasswordUtil;
import com.ideary.user.util.RedisUtil;
import com.ideary.user.util.EmailUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 用户服务实现类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-01
 */
@Service
@Transactional
public class UserServiceImpl implements UserService {

    private static final Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserProfileMapper userProfileMapper;

    @Autowired
    private UserFollowMapper userFollowMapper;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private PasswordUtil passwordUtil;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private EmailUtil emailUtil;

    // Redis Key前缀
    private static final String EMAIL_CODE_PREFIX = "email_code:";
    private static final String SMS_CODE_PREFIX = "sms_code:";
    private static final String LOGIN_ATTEMPTS_PREFIX = "login_attempts:";
    private static final String TOKEN_BLACKLIST_PREFIX = "token_blacklist:";

    @Override
    public UserLoginResponse register(UserRegisterRequest request) {
        logger.info("用户注册开始，用户名: {}, 邮箱: {}", request.getUsername(), request.getEmail());

        // 验证密码一致性
        if (!request.isPasswordMatch()) {
            throw new RuntimeException("两次输入的密码不一致");
        }

        // 验证用户协议
        if (!request.isAgreeTerms()) {
            throw new RuntimeException("请同意用户协议");
        }

        // 验证邮箱验证码
        if (!verifyEmailCode(request.getEmail(), request.getEmailCode(), "register")) {
            throw new RuntimeException("邮箱验证码错误或已过期");
        }

        // 检查用户名是否已存在
        if (userMapper.existsByUsername(request.getUsername())) {
            throw new RuntimeException("用户名已存在");
        }

        // 检查邮箱是否已存在
        if (userMapper.existsByEmail(request.getEmail())) {
            throw new RuntimeException("邮箱已被注册");
        }

        // 检查手机号是否已存在（如果提供）
        if (request.getPhone() != null && !request.getPhone().trim().isEmpty()) {
            if (userMapper.existsByPhone(request.getPhone())) {
                throw new RuntimeException("手机号已被注册");
            }
        }

        try {
            // 创建用户
            User user = new User();
            user.setUsername(request.getUsername());
            user.setEmail(request.getEmail());
            user.setPhone(request.getPhone());
            user.setPassword(passwordUtil.encode(request.getPassword()));
            user.setNickname(request.getNickname());
            user.setStatus(1); // 正常状态
            user.setEmailVerified(true); // 注册时已验证邮箱
            user.setPhoneVerified(false);
            user.setCreatedAt(LocalDateTime.now());
            user.setUpdatedAt(LocalDateTime.now());

            userMapper.insert(user);

            // 创建用户详细资料
            UserProfile profile = new UserProfile(user.getId());
            userProfileMapper.insert(profile);

            // 生成Token
            List<String> roles = List.of("USER"); // 默认角色
            String accessToken = jwtUtil.generateAccessToken(user.getId(), user.getUsername(), roles);
            String refreshToken = jwtUtil.generateRefreshToken(user.getId(), user.getUsername());

            // 构建响应
            UserLoginResponse response = new UserLoginResponse();
            response.setUserId(user.getId());
            response.setUsername(user.getUsername());
            response.setEmail(user.getEmail());
            response.setNickname(user.getNickname());
            response.setAvatar(user.getAvatar());
            response.setRoles(roles);
            response.setAccessToken(accessToken);
            response.setRefreshToken(refreshToken);
            response.setExpiresIn(jwtUtil.getExpiration() / 1000);
            response.setLoginTime(LocalDateTime.now());
            response.setFirstLogin(true);

            // 清除邮箱验证码
            redisUtil.delete(EMAIL_CODE_PREFIX + request.getEmail() + ":register");

            logger.info("用户注册成功，用户ID: {}, 用户名: {}", user.getId(), user.getUsername());
            return response;

        } catch (Exception e) {
            logger.error("用户注册失败，用户名: {}, 错误: {}", request.getUsername(), e.getMessage(), e);
            throw new RuntimeException("注册失败: " + e.getMessage());
        }
    }

    @Override
    public UserLoginResponse login(UserLoginRequest request) {
        logger.info("用户登录开始，账号: {}", request.getAccount());

        // 检查登录尝试次数
        String attemptsKey = LOGIN_ATTEMPTS_PREFIX + request.getAccount();
        Integer attempts = (Integer) redisUtil.get(attemptsKey);
        if (attempts != null && attempts >= 5) {
            throw new RuntimeException("登录尝试次数过多，请30分钟后再试");
        }

        try {
            // 查找用户
            User user = userMapper.findByAccount(request.getAccount());
            if (user == null) {
                // 增加登录尝试次数
                incrementLoginAttempts(request.getAccount());
                throw new RuntimeException("用户不存在");
            }

            // 检查用户状态
            if (!user.isActive()) {
                throw new RuntimeException("用户账号已被禁用或注销");
            }

            // 验证密码
            if (!passwordUtil.matches(request.getPassword(), user.getPassword())) {
                // 增加登录尝试次数
                incrementLoginAttempts(request.getAccount());
                throw new RuntimeException("密码错误");
            }

            // 验证验证码（如果需要）
            if (request.needCaptcha()) {
                // TODO: 实现验证码验证逻辑
            }

            // 获取用户角色
            List<String> roles = getUserRoles(user.getId());

            // 生成Token
            String accessToken = jwtUtil.generateAccessToken(user.getId(), user.getUsername(), roles);
            String refreshToken = jwtUtil.generateRefreshToken(user.getId(), user.getUsername());

            // 更新最后登录时间
            userMapper.updateLastLoginTime(user.getId(), LocalDateTime.now());

            // 构建响应
            UserLoginResponse response = new UserLoginResponse();
            response.setUserId(user.getId());
            response.setUsername(user.getUsername());
            response.setEmail(user.getEmail());
            response.setNickname(user.getNickname());
            response.setAvatar(user.getAvatar());
            response.setRoles(roles);
            response.setAccessToken(accessToken);
            response.setRefreshToken(refreshToken);
            response.setExpiresIn(jwtUtil.getExpiration() / 1000);
            response.setLoginTime(LocalDateTime.now());
            response.setFirstLogin(false);

            // 清除登录尝试次数
            redisUtil.delete(attemptsKey);

            logger.info("用户登录成功，用户ID: {}, 用户名: {}", user.getId(), user.getUsername());
            return response;

        } catch (Exception e) {
            logger.error("用户登录失败，账号: {}, 错误: {}", request.getAccount(), e.getMessage());
            throw e;
        }
    }

    @Override
    public void logout(Long userId, String token) {
        logger.info("用户登出，用户ID: {}", userId);

        try {
            // 将Token加入黑名单
            long remainingTime = jwtUtil.getTokenRemainingTime(token);
            if (remainingTime > 0) {
                redisUtil.set(TOKEN_BLACKLIST_PREFIX + token, "1", remainingTime, TimeUnit.SECONDS);
            }

            logger.info("用户登出成功，用户ID: {}", userId);
        } catch (Exception e) {
            logger.error("用户登出失败，用户ID: {}, 错误: {}", userId, e.getMessage());
            throw new RuntimeException("登出失败: " + e.getMessage());
        }
    }

    @Override
    public UserLoginResponse refreshToken(String refreshToken) {
        logger.info("刷新Token开始");

        try {
            // 验证刷新Token
            if (!jwtUtil.validateToken(refreshToken)) {
                throw new RuntimeException("刷新Token无效");
            }

            // 获取用户信息
            Long userId = jwtUtil.getUserIdFromToken(refreshToken);
            String username = jwtUtil.getUsernameFromToken(refreshToken);

            // 检查用户是否存在且状态正常
            User user = userMapper.selectById(userId);
            if (user == null || !user.isActive()) {
                throw new RuntimeException("用户不存在或已被禁用");
            }

            // 获取用户角色
            List<String> roles = getUserRoles(userId);

            // 生成新的访问Token
            String newAccessToken = jwtUtil.generateAccessToken(userId, username, roles);

            // 构建响应
            UserLoginResponse response = new UserLoginResponse();
            response.setUserId(userId);
            response.setUsername(username);
            response.setEmail(user.getEmail());
            response.setNickname(user.getNickname());
            response.setAvatar(user.getAvatar());
            response.setRoles(roles);
            response.setAccessToken(newAccessToken);
            response.setRefreshToken(refreshToken); // 保持原刷新Token
            response.setExpiresIn(jwtUtil.getExpiration() / 1000);
            response.setLoginTime(LocalDateTime.now());

            logger.info("Token刷新成功，用户ID: {}", userId);
            return response;

        } catch (Exception e) {
            logger.error("Token刷新失败，错误: {}", e.getMessage());
            throw new RuntimeException("Token刷新失败: " + e.getMessage());
        }
    }

    @Override
    public UserInfoResponse getUserInfo(Long userId) {
        logger.debug("获取用户信息，用户ID: {}", userId);

        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        return buildUserInfoResponse(user);
    }

    @Override
    public UserInfoResponse getUserInfoByUsername(String username) {
        logger.debug("根据用户名获取用户信息，用户名: {}", username);

        User user = userMapper.findByUsername(username);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        return buildUserInfoResponse(user);
    }

    // 私有辅助方法
    private void incrementLoginAttempts(String account) {
        String key = LOGIN_ATTEMPTS_PREFIX + account;
        Integer attempts = (Integer) redisUtil.get(key);
        attempts = attempts == null ? 1 : attempts + 1;
        redisUtil.set(key, attempts, 30, TimeUnit.MINUTES);
    }

    private List<String> getUserRoles(Long userId) {
        // TODO: 实现获取用户角色逻辑
        return List.of("USER");
    }

    private UserInfoResponse buildUserInfoResponse(User user) {
        UserInfoResponse response = new UserInfoResponse();
        BeanUtils.copyProperties(user, response);

        // 获取用户详细资料
        UserProfile profile = userProfileMapper.findByUserId(user.getId());
        if (profile != null) {
            BeanUtils.copyProperties(profile, response);
        }

        // 获取统计信息
        response.setFollowingCount(userFollowMapper.countFollowing(user.getId()));
        response.setFollowersCount(userFollowMapper.countFollowers(user.getId()));

        // 设置状态描述
        response.setStatusDescription(User.Status.fromCode(user.getStatus()).getDescription());

        // 设置性别描述
        if (response.getGender() != null) {
            response.setGenderDescription(UserProfile.Gender.fromCode(response.getGender()).getDescription());
        }

        return response;
    }

    @Override
    public UserInfoResponse updateUserInfo(Long userId, UserUpdateRequest request) {
        logger.info("更新用户基本信息，用户ID: {}", userId);

        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 更新用户信息
        if (request.getNickname() != null) {
            user.setNickname(request.getNickname());
        }
        if (request.getAvatar() != null) {
            user.setAvatar(request.getAvatar());
        }
        user.setUpdatedAt(LocalDateTime.now());

        userMapper.updateById(user);

        logger.info("用户基本信息更新成功，用户ID: {}", userId);
        return getUserInfo(userId);
    }

    @Override
    public UserInfoResponse updateUserProfile(Long userId, UserProfileUpdateRequest request) {
        logger.info("更新用户详细资料，用户ID: {}", userId);

        UserProfile profile = userProfileMapper.findByUserId(userId);
        if (profile == null) {
            profile = new UserProfile(userId);
        }

        // 更新详细资料
        BeanUtils.copyProperties(request, profile, "id", "userId", "createdAt");
        profile.setUpdatedAt(LocalDateTime.now());

        if (profile.getId() == null) {
            userProfileMapper.insert(profile);
        } else {
            userProfileMapper.updateById(profile);
        }

        logger.info("用户详细资料更新成功，用户ID: {}", userId);
        return getUserInfo(userId);
    }

    @Override
    public void changePassword(Long userId, PasswordChangeRequest request) {
        logger.info("修改密码，用户ID: {}", userId);

        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 验证原密码
        if (!passwordUtil.matches(request.getOldPassword(), user.getPassword())) {
            throw new RuntimeException("原密码错误");
        }

        // 验证新密码一致性
        if (!request.getNewPassword().equals(request.getConfirmPassword())) {
            throw new RuntimeException("两次输入的新密码不一致");
        }

        // 更新密码
        String encodedPassword = passwordUtil.encode(request.getNewPassword());
        userMapper.updatePassword(userId, encodedPassword);

        logger.info("密码修改成功，用户ID: {}", userId);
    }

    @Override
    public void sendEmailVerificationCode(String email, String type) {
        logger.info("发送邮箱验证码，邮箱: {}, 类型: {}", email, type);

        // 生成6位数字验证码
        String code = String.format("%06d", (int) (Math.random() * 1000000));

        // 存储到Redis，有效期10分钟
        String key = EMAIL_CODE_PREFIX + email + ":" + type;
        redisUtil.set(key, code, 10, TimeUnit.MINUTES);

        // 发送邮件
        String subject = getEmailSubject(type);
        String content = getEmailContent(type, code);
        emailUtil.sendSimpleMail(email, subject, content);

        logger.info("邮箱验证码发送成功，邮箱: {}", email);
    }

    @Override
    public boolean verifyEmailCode(String email, String code, String type) {
        String key = EMAIL_CODE_PREFIX + email + ":" + type;
        String storedCode = (String) redisUtil.get(key);
        return storedCode != null && storedCode.equals(code);
    }

    @Override
    public void followUser(Long followerId, Long followingId) {
        logger.info("关注用户，关注者ID: {}, 被关注者ID: {}", followerId, followingId);

        if (followerId.equals(followingId)) {
            throw new RuntimeException("不能关注自己");
        }

        // 检查被关注用户是否存在
        User followingUser = userMapper.selectById(followingId);
        if (followingUser == null || !followingUser.isActive()) {
            throw new RuntimeException("被关注的用户不存在或已被禁用");
        }

        // 检查是否已关注
        if (userFollowMapper.isFollowing(followerId, followingId)) {
            throw new RuntimeException("已经关注了该用户");
        }

        // 创建关注关系
        UserFollow follow = new UserFollow(followerId, followingId);
        follow.setCreatedAt(LocalDateTime.now());
        userFollowMapper.insert(follow);

        logger.info("关注成功，关注者ID: {}, 被关注者ID: {}", followerId, followingId);
    }

    @Override
    public void unfollowUser(Long followerId, Long followingId) {
        logger.info("取消关注，关注者ID: {}, 被关注者ID: {}", followerId, followingId);

        int result = userFollowMapper.unfollow(followerId, followingId);
        if (result == 0) {
            throw new RuntimeException("未关注该用户");
        }

        logger.info("取消关注成功，关注者ID: {}, 被关注者ID: {}", followerId, followingId);
    }

    @Override
    public boolean isFollowing(Long followerId, Long followingId) {
        return userFollowMapper.isFollowing(followerId, followingId);
    }

    @Override
    public boolean isUsernameAvailable(String username) {
        return !userMapper.existsByUsername(username);
    }

    @Override
    public boolean isEmailAvailable(String email) {
        return !userMapper.existsByEmail(email);
    }

    @Override
    public boolean isPhoneAvailable(String phone) {
        return !userMapper.existsByPhone(phone);
    }

    // 辅助方法
    private String getEmailSubject(String type) {
        return switch (type) {
            case "register" -> "Ideary - 注册验证码";
            case "reset_password" -> "Ideary - 密码重置验证码";
            case "verify_email" -> "Ideary - 邮箱验证码";
            default -> "Ideary - 验证码";
        };
    }

    private String getEmailContent(String type, String code) {
        String action = switch (type) {
            case "register" -> "注册";
            case "reset_password" -> "重置密码";
            case "verify_email" -> "验证邮箱";
            default -> "验证";
        };

        return String.format("""
                您好！

                您正在进行%s操作，验证码为：%s

                验证码有效期为10分钟，请及时使用。
                如果这不是您的操作，请忽略此邮件。

                Ideary团队
                """, action, code);
    }

    // 其他方法的实现...
    @Override
    public void resetPassword(PasswordResetRequest request) {
        // TODO: 实现密码重置逻辑
        throw new RuntimeException("功能开发中");
    }

    @Override
    public void verifyEmail(Long userId, String code) {
        // TODO: 实现邮箱验证逻辑
        throw new RuntimeException("功能开发中");
    }

    @Override
    public void sendSmsVerificationCode(String phone, String type) {
        // TODO: 实现短信验证码发送逻辑
        throw new RuntimeException("功能开发中");
    }

    @Override
    public boolean verifySmsCode(String phone, String code, String type) {
        // TODO: 实现短信验证码验证逻辑
        return false;
    }

    @Override
    public void verifyPhone(Long userId, String phone, String code) {
        // TODO: 实现手机号验证逻辑
        throw new RuntimeException("功能开发中");
    }

    @Override
    public List<UserInfoResponse> getFollowingList(Long userId, int page, int size) {
        // TODO: 实现获取关注列表逻辑
        return new ArrayList<>();
    }

    @Override
    public List<UserInfoResponse> getFollowersList(Long userId, int page, int size) {
        // TODO: 实现获取粉丝列表逻辑
        return new ArrayList<>();
    }

    @Override
    public List<UserInfoResponse> getMutualFollowsList(Long userId, int page, int size) {
        // TODO: 实现获取互相关注列表逻辑
        return new ArrayList<>();
    }

    @Override
    public String uploadAvatar(Long userId, byte[] avatarData) {
        // TODO: 实现头像上传逻辑
        throw new RuntimeException("功能开发中");
    }

    @Override
    public void disableUser(Long userId, String reason) {
        // TODO: 实现禁用用户逻辑
        throw new RuntimeException("功能开发中");
    }

    @Override
    public void enableUser(Long userId) {
        // TODO: 实现启用用户逻辑
        throw new RuntimeException("功能开发中");
    }

    @Override
    public void deleteUser(Long userId, String password) {
        // TODO: 实现注销用户逻辑
        throw new RuntimeException("功能开发中");
    }

    @Override
    public List<UserInfoResponse> searchUsers(String keyword, int page, int size) {
        // TODO: 实现搜索用户逻辑
        return new ArrayList<>();
    }

    @Override
    public UserStatsResponse getUserStats(Long userId) {
        // TODO: 实现获取用户统计信息逻辑
        return new UserStatsResponse();
    }

    @Override
    public List<UserInfoResponse> getUserInfoBatch(List<Long> userIds) {
        // TODO: 实现批量获取用户信息逻辑
        return new ArrayList<>();
    }
}
